# ROS环境清理脚本功能对比

## 脚本概述

| 脚本 | 位置 | 用途 |
|------|------|------|
| **原版脚本** | `tools/clean_ros_env.sh` | 基础ROS环境清理 |
| **增强版脚本** | `nav2_system_tests/src/gps_navigation/enhanced_clean_env.sh` | 专门针对GPS导航问题的增强清理 |

## 功能对比表

| 功能类别 | 原版脚本 | 增强版脚本 | 说明 |
|----------|----------|------------|------|
| **进程清理** | ✅ | ✅ | 增强版包含原版所有功能 |
| - killall清理 | ✅ | ✅ | 完全相同 |
| - 精准进程查找 | ✅ | ✅ | 完全相同，排除开发工具 |
| - static_transform_publisher特殊处理 | ✅ | ✅ | 完全相同 |
| **ROS2守护进程** | ✅ | ✅ | 增强版包含原版所有功能 |
| - 停止守护进程 | ✅ | ✅ | 完全相同 |
| - 重启守护进程 | ✅ | ✅ | 完全相同 |
| **环境变量管理** | ✅ | ✅ | 增强版包含原版所有功能 |
| - 保存当前环境变量 | ✅ | ✅ | 完全相同 |
| - 临时重置环境变量 | ✅ | ✅ | 完全相同 |
| - 恢复环境变量 | ✅ | ✅ | 完全相同 |
| **话题检查** | ✅ | ✅ | 增强版包含原版所有功能 |
| - 列出剩余话题 | ✅ | ✅ | 完全相同 |
| - tf_static话题检查 | ✅ | ✅ | 完全相同 |
| - 工作空间环境设置 | ✅ | ✅ | 完全相同 |
| **增强功能** | ❌ | ✅ | 增强版独有功能 |
| - 彩色日志输出 | ❌ | ✅ | 提高可读性 |
| - DDS缓存清理 | ❌ | ✅ | 解决时间跳跃问题 |
| - 共享内存清理 | ❌ | ✅ | 深度系统清理 |
| - Gazebo缓存清理 | ❌ | ✅ | 仿真环境清理 |
| - TF缓存清理 | ❌ | ✅ | 解决变换问题 |
| - 导航相关缓存清理 | ❌ | ✅ | 专门针对导航问题 |
| - 文件系统同步 | ❌ | ✅ | 确保数据一致性 |
| - 系统稳定等待 | ❌ | ✅ | 给系统恢复时间 |
| - 详细状态报告 | ❌ | ✅ | 便于问题诊断 |
| - 启动建议 | ❌ | ✅ | 用户指导 |

## 详细功能说明

### 原版脚本功能（已完全包含在增强版中）

1. **精准进程清理**
   ```bash
   # 使用killall清理常见进程
   killall -9 ros gz rviz2 robot_state_publisher ...
   
   # 精准查找并排除开发工具
   ps aux | grep -E "ros2|gz|..." | grep -v vscode | grep -v cursor
   ```

2. **环境变量保护**
   ```bash
   # 保存当前环境变量
   SAVED_ROS_DOMAIN_ID=$ROS_DOMAIN_ID
   SAVED_RMW_IMPLEMENTATION=$RMW_IMPLEMENTATION
   
   # 清理后恢复
   export ROS_DOMAIN_ID=$SAVED_ROS_DOMAIN_ID
   ```

3. **tf_static话题检查**
   ```bash
   if ros2 topic list | grep -q "/tf_static"; then
     echo "警告：/tf_static 话题仍然存在..."
   fi
   ```

### 增强版独有功能

1. **深度缓存清理**
   ```bash
   # DDS缓存
   rm -rf ~/.ros/log/*
   rm -rf /tmp/.ros*
   
   # 共享内存清理
   ipcs -m | awk '/^0x/ {print $2}' | xargs -r ipcrm -m
   
   # Gazebo缓存
   rm -rf ~/.gazebo/log/*
   rm -rf ~/.gz/log/*
   ```

2. **专门的导航缓存清理**
   ```bash
   rm -rf /tmp/nav2_*
   rm -rf /tmp/costmap_*
   rm -rf /tmp/amcl_*
   ```

3. **彩色日志和状态报告**
   ```bash
   log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
   log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
   ```

## 使用建议

### 何时使用原版脚本
- 一般的ROS开发和测试
- 快速环境重置
- 不涉及复杂仿真的场景

### 何时使用增强版脚本
- **GPS导航测试**（强烈推荐）
- 出现时间跳跃问题时
- 复杂的仿真环境
- 需要深度清理缓存时
- 调试导航相关问题时

### 兼容性说明
- 增强版脚本**完全兼容**原版脚本的所有功能
- 可以直接替换原版脚本使用
- 增强版脚本执行时间稍长（约多10-15秒），但清理更彻底

## 推荐使用方式

对于GPS导航测试，建议使用增强版脚本：

```bash
# 使用增强版脚本
cd /opt/overlay_ws/src/navigation2
./nav2_system_tests/src/gps_navigation/enhanced_clean_env.sh

# 等待系统稳定
sleep 5

# 启动导航测试
source /opt/overlay_ws/install/setup.bash
ros2 launch nav2_system_tests event_driven_launch.py
```

## 总结

**增强版脚本 = 原版脚本的所有功能 + 专门针对GPS导航问题的增强清理**

- ✅ **100%包含**原版脚本的所有功能
- ✅ **新增**深度缓存清理功能
- ✅ **专门解决**时间跳跃和导航问题
- ✅ **更好的**用户体验和问题诊断

因此，您可以放心使用增强版脚本替代原版脚本，特别是在进行GPS导航测试时。
