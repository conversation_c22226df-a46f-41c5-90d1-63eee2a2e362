#!/bin/bash

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0

# 安全版环境清理脚本
# 专门用于解决GPS导航测试中的时间跳跃和缓存问题

echo "=========================================="
echo "安全版ROS环境清理脚本"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 获取当前脚本的PID，避免杀死自己
SCRIPT_PID=$$
log_info "当前脚本PID: $SCRIPT_PID"

# 1. 保存当前环境变量
log_info "保存当前ROS环境变量..."
SAVED_ROS_DOMAIN_ID=$ROS_DOMAIN_ID
SAVED_RMW_IMPLEMENTATION=$RMW_IMPLEMENTATION

# 2. 安全地停止ROS相关进程
log_info "安全地停止ROS相关进程..."

# 首先尝试优雅地停止ROS2守护进程
log_info "停止ROS2守护进程..."
ros2 daemon stop 2>/dev/null || true
sleep 1

# 安全地清理特定进程（避免杀死脚本自身和重要系统进程）
log_info "清理特定ROS进程..."

# 清理gazebo相关进程
pkill -f "gz sim" 2>/dev/null || true
pkill -f "gazebo" 2>/dev/null || true

# 清理rviz
pkill -f "rviz2" 2>/dev/null || true

# 清理nav2相关进程
pkill -f "nav2_bringup" 2>/dev/null || true
pkill -f "controller_server" 2>/dev/null || true
pkill -f "planner_server" 2>/dev/null || true
pkill -f "bt_navigator" 2>/dev/null || true
pkill -f "amcl" 2>/dev/null || true

# 清理其他ROS节点
pkill -f "robot_state_publisher" 2>/dev/null || true
pkill -f "static_transform_publisher" 2>/dev/null || true
pkill -f "map_server" 2>/dev/null || true
pkill -f "lifecycle_manager" 2>/dev/null || true

sleep 2

# 3. 清理缓存文件
log_info "清理ROS2缓存..."
rm -rf ~/.ros/log/* 2>/dev/null || true
rm -rf /tmp/.ros* 2>/dev/null || true

# 清理特定的临时文件
log_info "清理导航相关缓存..."
find /tmp -name "nav2_*" -type f -delete 2>/dev/null || true
find /tmp -name "costmap_*" -type f -delete 2>/dev/null || true
find /tmp -name "amcl_*" -type f -delete 2>/dev/null || true
find /tmp -name "tf_*" -type f -delete 2>/dev/null || true

# 清理Gazebo缓存
log_info "清理Gazebo缓存..."
rm -rf ~/.gazebo/log/* 2>/dev/null || true
rm -rf ~/.gz/log/* 2>/dev/null || true
find /tmp -name "gazebo*" -type f -delete 2>/dev/null || true
find /tmp -name "gz*" -type f -delete 2>/dev/null || true

# 4. 重置环境变量
log_info "重置ROS环境变量..."
unset ROS_DOMAIN_ID
unset RMW_IMPLEMENTATION
unset CYCLONEDDS_URI
unset FASTRTPS_DEFAULT_PROFILES_FILE

# 5. 重启ROS2守护进程
log_info "重启ROS2守护进程..."
ros2 daemon start 2>/dev/null || true
sleep 2

# 6. 恢复环境变量
log_info "恢复ROS环境变量..."
if [ -n "$SAVED_ROS_DOMAIN_ID" ]; then
    export ROS_DOMAIN_ID=$SAVED_ROS_DOMAIN_ID
    log_success "恢复 ROS_DOMAIN_ID=$ROS_DOMAIN_ID"
else
    export ROS_DOMAIN_ID=11
    log_info "设置默认 ROS_DOMAIN_ID=11"
fi

if [ -n "$SAVED_RMW_IMPLEMENTATION" ]; then
    export RMW_IMPLEMENTATION=$SAVED_RMW_IMPLEMENTATION
    log_success "恢复 RMW_IMPLEMENTATION=$RMW_IMPLEMENTATION"
else
    export RMW_IMPLEMENTATION=rmw_cyclonedds_cpp
    log_info "设置推荐的 RMW_IMPLEMENTATION=rmw_cyclonedds_cpp"
fi

# 设置推荐的DDS配置
export CYCLONEDDS_URI='<Discovery><Peers><Peer address="localhost"/></Peers></Discovery>'

# 7. 验证环境
log_info "设置工作空间环境..."
source /opt/overlay_ws/install/setup.bash 2>/dev/null || true

log_info "检查话题列表..."
TOPICS=$(timeout 5s ros2 topic list 2>/dev/null || echo "无法获取话题列表")
echo "$TOPICS"

# 检查tf_static话题
if echo "$TOPICS" | grep -q "/tf_static"; then
    log_warn "/tf_static 话题仍然存在"
else
    log_success "/tf_static 话题已清理"
fi

# 8. 同步文件系统
log_info "同步文件系统..."
sync

# 9. 等待系统稳定
log_info "等待系统稳定..."
sleep 3

echo "=========================================="
log_success "环境清理完成！"
echo "=========================================="

# 显示当前环境状态
echo "当前环境状态:"
echo "- ROS_DOMAIN_ID: ${ROS_DOMAIN_ID:-未设置}"
echo "- RMW_IMPLEMENTATION: ${RMW_IMPLEMENTATION:-未设置}"
echo "- 脚本PID: $SCRIPT_PID"
echo "- 工作目录: $(pwd)"
echo "- 时间: $(date)"

echo ""
echo "建议的启动步骤:"
echo "1. 等待5秒让系统完全稳定"
echo "2. 运行: source /opt/overlay_ws/install/setup.bash"
echo "3. 运行: ros2 launch nav2_system_tests event_driven_launch.py"

echo ""
log_success "脚本执行完成！现在可以安全启动导航系统。"
