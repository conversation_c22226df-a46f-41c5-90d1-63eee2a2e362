#!/bin/bash

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0

# 增强版环境清理脚本
# 专门用于解决GPS导航测试中的时间跳跃和缓存问题

# 不使用 set -e，因为我们希望脚本在遇到错误时继续执行
# set -e

# 信号处理器 - 防止脚本被意外中断
cleanup_on_exit() {
    echo ""
    echo "脚本被中断，正在进行最后的清理..."
    # 确保ROS2守护进程重启
    ros2 daemon stop 2>/dev/null || true
    ros2 daemon start 2>/dev/null || true
    echo "清理完成，退出。"
    exit 1
}

# 捕获中断信号
trap cleanup_on_exit INT TERM

echo "=========================================="
echo "增强版ROS环境清理脚本"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 1. 停止所有ROS相关进程（包含原版脚本的精准清理）
log_info "停止所有ROS相关进程..."

# 使用原版脚本的精准清理方法（增加安全检查）
log_info "使用killall清理常见进程..."
if command -v killall >/dev/null 2>&1; then
    killall -9 ros gz rviz2 robot_state_publisher component_container_isolated map_server lifecycle_manager nav2_bringup turtlebot3_waffle foxglove_bridge rqt static_transform_publisher 2>/dev/null || true
else
    log_warn "killall命令不可用，跳过此步骤"
fi

# 精准查找并杀死ROS/Gazebo/RViz相关进程，排除vscode/cursor等远程开发进程
log_info "精准清理ROS相关进程（排除开发工具）..."
if command -v ps >/dev/null 2>&1; then
    # 分步执行，避免管道中断
    PIDS=$(ps aux | grep -E "ros2|gz|rviz|robot_state_publisher|component_container|map_server|lifecycle_manager|nav2|turtlebot|foxglove|rqt|static_transform_publisher" | \
           grep -v grep | grep -v vscode | grep -v cursor | awk '{print $2}' 2>/dev/null || true)

    if [ -n "$PIDS" ]; then
        echo "$PIDS" | xargs -r kill -9 2>/dev/null || true
        log_info "清理了进程: $PIDS"
    else
        log_info "未找到需要清理的ROS进程"
    fi
else
    log_warn "ps命令不可用，跳过此步骤"
fi

# 特别处理static_transform_publisher
log_info "特别清理static_transform_publisher..."
if command -v ps >/dev/null 2>&1; then
    STATIC_PIDS=$(ps aux | grep static_transform_publisher | grep -v grep | awk '{print $2}' 2>/dev/null || true)
    if [ -n "$STATIC_PIDS" ]; then
        echo "$STATIC_PIDS" | xargs -r kill -9 2>/dev/null || true
        log_info "清理了static_transform_publisher进程: $STATIC_PIDS"
    fi
fi

sleep 2

# 2. 清理ROS2 DDS缓存
log_info "清理ROS2 DDS缓存..."
rm -rf ~/.ros/log/* 2>/dev/null || true
rm -rf /tmp/.ros* 2>/dev/null || true
rm -rf /tmp/ros* 2>/dev/null || true

# 3. 清理DDS发现缓存
log_info "清理DDS发现缓存..."
if [ -d "/tmp/dds_discovery" ]; then
    rm -rf /tmp/dds_discovery/* 2>/dev/null || true
fi

# 4. 清理共享内存
log_info "清理共享内存..."
if command -v ipcs >/dev/null 2>&1; then
    # 清理共享内存段
    ipcs -m | awk '/^0x/ {print $2}' | xargs -r ipcrm -m 2>/dev/null || true
    # 清理信号量
    ipcs -s | awk '/^0x/ {print $2}' | xargs -r ipcrm -s 2>/dev/null || true
    # 清理消息队列
    ipcs -q | awk '/^0x/ {print $2}' | xargs -r ipcrm -q 2>/dev/null || true
fi

# 5. 保存和重置ROS环境变量（原版脚本功能）
log_info "保存当前ROS环境变量..."
SAVED_ROS_DOMAIN_ID=$ROS_DOMAIN_ID
SAVED_RMW_IMPLEMENTATION=$RMW_IMPLEMENTATION

log_info "暂时重置ROS环境变量以清理环境..."
unset ROS_DOMAIN_ID
unset RMW_IMPLEMENTATION

# 6. 清理Gazebo缓存
log_info "清理Gazebo缓存..."
rm -rf ~/.gazebo/log/* 2>/dev/null || true
rm -rf ~/.gz/log/* 2>/dev/null || true
rm -rf /tmp/gazebo* 2>/dev/null || true
rm -rf /tmp/gz* 2>/dev/null || true

# 7. 清理TF缓存相关的临时文件
log_info "清理TF缓存..."
rm -rf /tmp/tf_* 2>/dev/null || true
rm -rf /tmp/transform_* 2>/dev/null || true

# 8. 重启ROS2守护进程（原版脚本方法）
log_info "停止ROS2守护进程..."
ros2 daemon stop || true

log_info "重新启动ROS2守护进程..."
ros2 daemon start
sleep 2

# 9. 验证守护进程状态
log_info "验证ROS2守护进程状态..."
if ros2 daemon status | grep -q "running"; then
    log_success "ROS2守护进程运行正常"
else
    log_warn "ROS2守护进程状态异常"
fi

# 10. 清理环境变量缓存
log_info "清理环境变量缓存..."
unset RMW_IMPLEMENTATION
unset CYCLONEDDS_URI
unset FASTRTPS_DEFAULT_PROFILES_FILE

# 11. 恢复ROS环境变量（原版脚本功能）
log_info "恢复ROS环境变量..."
if [ -n "$SAVED_ROS_DOMAIN_ID" ]; then
    export ROS_DOMAIN_ID=$SAVED_ROS_DOMAIN_ID
    log_success "恢复 ROS_DOMAIN_ID=$ROS_DOMAIN_ID"
else
    export ROS_DOMAIN_ID=11
    log_info "设置默认 ROS_DOMAIN_ID=11"
fi

if [ -n "$SAVED_RMW_IMPLEMENTATION" ]; then
    export RMW_IMPLEMENTATION=$SAVED_RMW_IMPLEMENTATION
    log_success "恢复 RMW_IMPLEMENTATION=$RMW_IMPLEMENTATION"
else
    export RMW_IMPLEMENTATION=rmw_cyclonedds_cpp
    log_info "设置推荐的 RMW_IMPLEMENTATION=rmw_cyclonedds_cpp"
fi

# 设置推荐的DDS配置
export CYCLONEDDS_URI='<Discovery><Peers><Peer address="localhost"/></Peers></Discovery>'

# 12. 验证话题列表（原版脚本功能）
log_info "设置工作空间环境..."
source /opt/overlay_ws/install/setup.bash

log_info "检查剩余话题..."
ros2 topic list

log_info "验证ROS2通信..."
if timeout 5s ros2 topic list >/dev/null 2>&1; then
    log_success "ROS2通信正常"
else
    log_warn "ROS2通信可能存在问题"
fi

# 检查tf_static话题（原版脚本功能）
if ros2 topic list | grep -q "/tf_static"; then
    log_warn "/tf_static 话题仍然存在。如果需要完全清除，请尝试重新启动终端或者重启容器。"
else
    log_success "/tf_static 话题已清理"
fi

# 13. 清理特定的导航相关缓存
log_info "清理导航相关缓存..."
rm -rf /tmp/nav2_* 2>/dev/null || true
rm -rf /tmp/costmap_* 2>/dev/null || true
rm -rf /tmp/amcl_* 2>/dev/null || true

# 14. 同步文件系统
log_info "同步文件系统..."
sync

# 15. 等待系统稳定
log_info "等待系统稳定..."
sleep 3

echo "=========================================="
log_success "环境清理完成！"
echo "=========================================="

# 16. 显示当前环境状态
echo "当前环境状态:"
echo "- ROS_DOMAIN_ID: ${ROS_DOMAIN_ID:-未设置}"
echo "- RMW_IMPLEMENTATION: ${RMW_IMPLEMENTATION:-未设置}"
echo "- 工作目录: $(pwd)"
echo "- 时间: $(date)"

# 17. 提供启动建议
echo ""
echo "建议的启动步骤:"
echo "1. 等待5秒让系统完全稳定"
echo "2. 运行: source /opt/overlay_ws/install/setup.bash"
echo "3. 运行: ros2 launch nav2_system_tests event_driven_launch.py"

echo ""
log_success "脚本执行完成！"
