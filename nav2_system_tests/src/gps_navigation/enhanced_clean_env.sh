#!/bin/bash

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0

# 增强版环境清理脚本
# 专门用于解决GPS导航测试中的时间跳跃和缓存问题

set -e

echo "=========================================="
echo "增强版ROS环境清理脚本"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 1. 停止所有ROS相关进程
log_info "停止所有ROS相关进程..."
pkill -f "ros2" || true
pkill -f "gazebo" || true
pkill -f "gz" || true
pkill -f "rviz" || true
pkill -f "nav2" || true
pkill -f "amcl" || true
pkill -f "controller" || true
pkill -f "planner" || true
sleep 2

# 2. 清理ROS2 DDS缓存
log_info "清理ROS2 DDS缓存..."
rm -rf ~/.ros/log/* 2>/dev/null || true
rm -rf /tmp/.ros* 2>/dev/null || true
rm -rf /tmp/ros* 2>/dev/null || true

# 3. 清理DDS发现缓存
log_info "清理DDS发现缓存..."
if [ -d "/tmp/dds_discovery" ]; then
    rm -rf /tmp/dds_discovery/* 2>/dev/null || true
fi

# 4. 清理共享内存
log_info "清理共享内存..."
if command -v ipcs >/dev/null 2>&1; then
    # 清理共享内存段
    ipcs -m | awk '/^0x/ {print $2}' | xargs -r ipcrm -m 2>/dev/null || true
    # 清理信号量
    ipcs -s | awk '/^0x/ {print $2}' | xargs -r ipcrm -s 2>/dev/null || true
    # 清理消息队列
    ipcs -q | awk '/^0x/ {print $2}' | xargs -r ipcrm -q 2>/dev/null || true
fi

# 5. 重置ROS域ID
log_info "重置ROS域ID..."
export ROS_DOMAIN_ID=11
echo "ROS_DOMAIN_ID设置为: $ROS_DOMAIN_ID"

# 6. 清理Gazebo缓存
log_info "清理Gazebo缓存..."
rm -rf ~/.gazebo/log/* 2>/dev/null || true
rm -rf ~/.gz/log/* 2>/dev/null || true
rm -rf /tmp/gazebo* 2>/dev/null || true
rm -rf /tmp/gz* 2>/dev/null || true

# 7. 清理TF缓存相关的临时文件
log_info "清理TF缓存..."
rm -rf /tmp/tf_* 2>/dev/null || true
rm -rf /tmp/transform_* 2>/dev/null || true

# 8. 重启ROS2守护进程
log_info "重启ROS2守护进程..."
ros2 daemon stop 2>/dev/null || true
sleep 2
ros2 daemon start
sleep 2

# 9. 验证守护进程状态
log_info "验证ROS2守护进程状态..."
if ros2 daemon status | grep -q "running"; then
    log_success "ROS2守护进程运行正常"
else
    log_warn "ROS2守护进程状态异常"
fi

# 10. 清理环境变量缓存
log_info "清理环境变量缓存..."
unset RMW_IMPLEMENTATION
unset CYCLONEDDS_URI
unset FASTRTPS_DEFAULT_PROFILES_FILE

# 11. 设置推荐的DDS配置
log_info "设置推荐的DDS配置..."
export RMW_IMPLEMENTATION=rmw_cyclonedds_cpp
export CYCLONEDDS_URI='<Discovery><Peers><Peer address="localhost"/></Peers></Discovery>'

# 12. 验证话题列表
log_info "验证ROS2通信..."
timeout 5s ros2 topic list >/dev/null 2>&1 && log_success "ROS2通信正常" || log_warn "ROS2通信可能存在问题"

# 13. 清理特定的导航相关缓存
log_info "清理导航相关缓存..."
rm -rf /tmp/nav2_* 2>/dev/null || true
rm -rf /tmp/costmap_* 2>/dev/null || true
rm -rf /tmp/amcl_* 2>/dev/null || true

# 14. 同步文件系统
log_info "同步文件系统..."
sync

# 15. 等待系统稳定
log_info "等待系统稳定..."
sleep 3

echo "=========================================="
log_success "环境清理完成！"
echo "=========================================="

# 16. 显示当前环境状态
echo "当前环境状态:"
echo "- ROS_DOMAIN_ID: ${ROS_DOMAIN_ID:-未设置}"
echo "- RMW_IMPLEMENTATION: ${RMW_IMPLEMENTATION:-未设置}"
echo "- 工作目录: $(pwd)"
echo "- 时间: $(date)"

# 17. 提供启动建议
echo ""
echo "建议的启动步骤:"
echo "1. 等待5秒让系统完全稳定"
echo "2. 运行: source /opt/overlay_ws/install/setup.bash"
echo "3. 运行: ros2 launch nav2_system_tests event_driven_launch.py"

echo ""
log_success "脚本执行完成！"
