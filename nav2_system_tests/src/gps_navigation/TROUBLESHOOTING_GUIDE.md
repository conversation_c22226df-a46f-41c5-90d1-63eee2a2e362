# Navigation2 GPS导航故障排除指南

## 问题概述

在Navigation2 GPS导航测试中，经常遇到以下错误：

1. **"Failed to make progress"** (错误代码105)
2. **"Costmap timed out waiting for update"** (错误代码107)
3. **TF时间跳跃警告**

## 根本原因分析

### 1. "Failed to make progress" 错误

**原因：**
- `SimpleProgressChecker`检测到机器人在指定时间内没有移动足够距离
- 默认配置过于严格：`required_movement_radius: 0.5m`, `movement_time_allowance: 10.0s`
- MPPI控制器参数配置过于保守，导致无法生成有效轨迹

**解决方案：**
```yaml
progress_checker:
  plugin: "nav2_controller::SimpleProgressChecker"
  required_movement_radius: 0.3  # 从0.5降低到0.3
  movement_time_allowance: 15.0  # 从10.0增加到15.0
```

### 2. "Costmap timed out" 错误

**原因：**
- Costmap更新超时（默认300ms）
- TF变换延迟或不稳定
- 传感器数据处理延迟

**解决方案：**
```yaml
controller_server:
  ros__parameters:
    costmap_update_timeout: 1.0  # 从0.3增加到1.0秒
    failure_tolerance: 2.0       # 从0.3增加到2.0秒
```

### 3. TF时间跳跃问题

**原因：**
- ROS节点缓存了旧的时间戳数据
- DDS缓存污染
- 仿真时间重置但节点状态未清理

**解决方案：**
- 使用增强版清理脚本
- 设置正确的DDS配置
- 确保时间同步

## 完整修复方案

### 1. 参数配置优化

#### Controller Server
```yaml
controller_server:
  ros__parameters:
    controller_frequency: 15.0
    failure_tolerance: 2.0
    costmap_update_timeout: 1.0
    
    progress_checker:
      required_movement_radius: 0.3
      movement_time_allowance: 15.0
```

#### Costmap配置
```yaml
local_costmap:
  local_costmap:
    ros__parameters:
      transform_tolerance: 3.0
      update_frequency: 2.0
      publish_frequency: 2.0

global_costmap:
  global_costmap:
    ros__parameters:
      transform_tolerance: 3.0
      update_frequency: 0.5
      publish_frequency: 0.5
```

#### MPPI控制器优化
```yaml
FollowPath:
  plugin: "nav2_mppi_controller::MPPIController"
  vx_max: 0.3          # 降低最大速度
  vx_min: -0.2
  vy_max: 0.3
  wz_max: 1.5
  transform_tolerance: 0.2
  
  # Critics权重优化
  ConstraintCritic:
    cost_weight: 2.0   # 降低约束权重
  GoalCritic:
    cost_weight: 8.0   # 增加目标权重
  CostCritic:
    cost_weight: 2.0   # 降低代价权重
```

### 2. 环境清理流程

#### 使用增强版清理脚本
```bash
# 运行增强版清理脚本
./nav2_system_tests/src/gps_navigation/enhanced_clean_env.sh

# 等待5秒系统稳定
sleep 5

# 设置环境
source /opt/overlay_ws/install/setup.bash

# 启动导航
ros2 launch nav2_system_tests event_driven_launch.py
```

#### 手动清理步骤
```bash
# 1. 停止所有ROS进程
pkill -f "ros2"
pkill -f "gazebo"

# 2. 清理缓存
rm -rf ~/.ros/log/*
rm -rf /tmp/.ros*
rm -rf /tmp/ros*

# 3. 重启ROS2守护进程
ros2 daemon stop
ros2 daemon start

# 4. 设置环境变量
export ROS_DOMAIN_ID=11
export RMW_IMPLEMENTATION=rmw_cyclonedds_cpp
```

### 3. 监控和诊断

#### 关键话题监控
```bash
# 监控控制器状态
ros2 topic echo /controller_server/transition_event

# 监控TF状态
ros2 run tf2_tools view_frames.py

# 监控costmap更新
ros2 topic hz /local_costmap/costmap_raw
ros2 topic hz /global_costmap/costmap_raw
```

#### 日志分析
关注以下关键日志：
- `Failed to make progress`
- `Costmap timed out`
- `Detected jump back in time`
- `TF Exception`

## 预防措施

### 1. 启动前检查
- 确保环境完全清理
- 验证TF链完整性
- 检查传感器数据流

### 2. 参数调优原则
- 逐步调整，不要一次性大幅修改
- 优先调整时间容忍度参数
- 根据实际机器人性能调整速度限制

### 3. 系统监控
- 定期检查系统资源使用
- 监控网络延迟
- 观察机器人实际运动表现

## 常见问题FAQ

**Q: 为什么清理脚本这么重要？**
A: ROS2的DDS层会缓存消息和状态，重启仿真时如果不清理，会导致时间戳不一致。

**Q: 如何判断修复是否有效？**
A: 观察日志中是否还出现"Failed to make progress"和"Costmap timed out"错误。

**Q: 参数调整的优先级是什么？**
A: 1) 时间容忍度 2) 移动距离阈值 3) 控制器权重 4) 速度限制

**Q: 如果问题仍然存在怎么办？**
A: 1) 检查硬件性能 2) 验证传感器数据质量 3) 考虑降低控制频率

## 总结

通过以上修复方案，可以有效解决Navigation2 GPS导航中的进度检查和costmap超时问题。关键是：

1. **正确的参数配置** - 放宽时间和距离约束
2. **彻底的环境清理** - 避免缓存污染
3. **持续的监控** - 及时发现和解决问题

记住：**每次启动前都要运行清理脚本！**
